import Dexie, { Table } from 'dexie';

export interface Script {
  message_id: string; // Primary key
  displayCode: string;
  updated_at: number;
  miniapp_id?: string;
  name?: string;
  version?: number;
}

class WebScriptDB extends Dexie {
  scripts!: Table<Script>;

  constructor() {
    super('mysta-agent');

    this.version(9)
      .stores({
        scripts: 'message_id, updated_at, miniapp_id',
      });
  }

  // Script operations
  async saveScript(script: Script): Promise<void> {
    await this.scripts.put(script);
  }

  async getScript(messageId: string): Promise<Script | null> {
    const script = await this.scripts.get(messageId);
    return script || null;
  }

  async deleteScript(messageId: string): Promise<void> {
    await this.scripts.delete(messageId);
  }

  async getAllScripts(): Promise<Script[]> {
    return await this.scripts.toArray();
  }
}

let dbInstance = new WebScriptDB();

export async function resetDB() {
  await dbInstance.delete();
  dbInstance = new WebScriptDB();
  return dbInstance;
}

const dbProxy = new Proxy(
  {},
  {
    get(_, prop) {
      return dbInstance[prop as keyof WebScriptDB];
    },
  }
);

export const webScriptDB = dbProxy as WebScriptDB;
