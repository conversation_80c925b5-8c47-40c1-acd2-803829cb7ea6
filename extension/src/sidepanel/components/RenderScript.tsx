import React from 'react';
import DevIcon from '~/assets/icons/dev.svg';

interface RenderScriptProps {
  name?: string; // miniapp name
  version?: number; // miniapp version
  code?: string; // miniapp code
  miniapp_id?: string; // miniapp id
  message_id?: string; // message id
}

const RenderScript: React.FC<RenderScriptProps> = ({
  name,
  version,
  code,
  miniapp_id,
  message_id,
}) => {
  // Use mock data for now
  const mockData = {
    name: 'Web top-pinning plugin',
    version: 3,
    code: `<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Web top-pinning plugin</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      text-align: center;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
    }
    .header {
      background-color: #fff;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .logo {
      font-size: 48px;
      color: #3385ff;
    }
    .search-container {
      margin-top: 100px;
    }
    .search-box {
      width: 400px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 20px;
      font-size: 18px;
    }
    .search-button {
      padding: 10px 20px;
      border: none;
      border-radius: 20px;
      background-color: #3385ff;
      color: white;
      font-size: 18px;
      cursor: pointer;
      margin-left: 10px;
    }
    .search-button:hover {
      background-color: #0056b3;
    }
  </style>
</head>`,
    miniapp_id: 'web-top-pinning-plugin',
    message_id: 'msg-12345',
  };

  const displayName = name || mockData.name;
  const displayVersion = version || mockData.version;
  const displayCode = code || mockData.code;
  const displayMiniappId = miniapp_id || mockData.miniapp_id;
  const displayMessageId = message_id || mockData.message_id;

  const handleViewCode = () => {
    const webUrl = process.env.PLASMO_PUBLIC_WEB_URL || 'https://mysta.ai';

    // Get user's locale from browser, default to 'en'
    const locale = navigator.language.split('-')[0] || 'en';

    // Encode the code for URL transmission
    const encodedCode = encodeURIComponent(displayCode);

    // Check if URL would be too long (most browsers limit to ~2000 chars)
    const baseUrl = `${webUrl}/${locale}/miniapp/${displayMiniappId}/script/${displayMessageId}`;
    const fullUrl = `${baseUrl}?code=${encodedCode}`;

    if (fullUrl.length > 2000) {
      // For very large content, use base64 encoding to reduce size
      const base64Code = btoa(displayCode);
      const url = `${baseUrl}?code64=${encodeURIComponent(base64Code)}`;
      window.open(url, '_blank');
    } else {
      // Use direct encoding for smaller content
      window.open(fullUrl, '_blank');
    }
  };

  const handleTest = () => {
    // TODO: Implement test functionality
    console.log('Test script');
  };

  const handleInstall = () => {
    // TODO: Implement install functionality
    console.log('Install script');
  };

  return (
    <div
      style={{
        border: '1px solid #d1d5db',
        borderRadius: '12px',
        backgroundColor: '#ffffff',
        overflow: 'hidden',
        margin: '8px 0',
        width: '100%',
      }}
    >
      {/* Upper Section */}
      <div
        style={{
          padding: '16px',
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px',
        }}
      >
        {/* Icon */}
        <div
          style={{
            width: '48px',
            height: '48px',
            flexShrink: 0,
          }}
        >
          <img
            src={DevIcon}
            alt="Dev Icon"
            style={{
              width: '100%',
              height: '100%',
              borderRadius: '12px',
            }}
          />
        </div>

        {/* Content */}
        <div style={{ flex: 1, minWidth: 0 }}>
          <h3
            style={{
              margin: '0 0 4px 0',
              fontSize: '16px',
              fontWeight: 600,
              color: '#111827',
              lineHeight: '1.25',
            }}
          >
            {displayName}
          </h3>

          <p
            style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              color: '#6b7280',
              lineHeight: '1.4',
            }}
          >
            Version {displayVersion}
          </p>

          <button
            onClick={handleViewCode}
            style={{
              background: 'none',
              border: 'none',
              color: '#6b7280',
              fontSize: '14px',
              cursor: 'pointer',
              padding: 0,
              textDecoration: 'underline',
              lineHeight: '1.4',
            }}
            onMouseOver={e => {
              e.currentTarget.style.color = '#374151';
            }}
            onMouseOut={e => {
              e.currentTarget.style.color = '#6b7280';
            }}
          >
            Click to view the code
          </button>
        </div>
      </div>

      {/* Lower Section */}
      <div
        style={{
          borderTop: '1px solid #f3f4f6',
          padding: '12px 16px',
          display: 'flex',
          gap: '8px',
        }}
      >
        <button
          onClick={handleTest}
          style={{
            flex: 1,
            padding: '8px 16px',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            backgroundColor: '#ffffff',
            color: '#374151',
            fontSize: '14px',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
          }}
          onMouseOver={e => {
            e.currentTarget.style.backgroundColor = '#f9fafb';
            e.currentTarget.style.borderColor = '#9ca3af';
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor = '#ffffff';
            e.currentTarget.style.borderColor = '#d1d5db';
          }}
        >
          Test
        </button>

        <button
          onClick={handleInstall}
          style={{
            flex: 1,
            padding: '8px 16px',
            border: 'none',
            borderRadius: '8px',
            backgroundColor: '#374151',
            color: '#ffffff',
            fontSize: '14px',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'background-color 0.2s ease',
          }}
          onMouseOver={e => {
            e.currentTarget.style.backgroundColor = '#1f2937';
          }}
          onMouseOut={e => {
            e.currentTarget.style.backgroundColor = '#374151';
          }}
        >
          Install
        </button>
      </div>
    </div>
  );
};

export default RenderScript;
